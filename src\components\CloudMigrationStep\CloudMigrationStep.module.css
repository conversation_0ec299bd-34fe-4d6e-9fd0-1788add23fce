@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024 from breakpoints;

.step_container {
  max-width: 1192px;
  margin: 0 auto;
  padding: 20px 0;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }
}

.step_info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

.step_text {
  font-size: 18px;
  font-weight: 600;
  color: colorBlack;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 16px;
  }
}

.progress_text {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.progress_bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress_fill {
  height: 100%;
  background: linear-gradient(90deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}
