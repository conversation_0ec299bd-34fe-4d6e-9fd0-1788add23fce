'use client';

import { useState } from 'react';
import Image from 'next/image';
import styles from './CloudMigrationForm.module.css';
import useForm from '@hooks/useForm';
import Button from '@components/Button';
import getUserLocation from '@utils/getUserLocation';

export default function CloudMigrationForm({
  formData,
  source = 'CloudMigrationCalculator',
  handleResult,
  handleVisibleSection,
}: any) {
  const {
    title,
    instructions,
    consent_statement,
    LinkedInButton_title,
    button,
    formFields: {
      fieldNameFor_FirstName,
      fieldNameFor_LastName,
      fieldNameFor_EmailAddress,
      fieldNameFor_CompanyName,
      fieldNameFor_PhoneNumber,
      fieldNameFor_HowCanWeHelpYou,
    },
  } = formData;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmitCloudMigration,
  } = useForm(
    {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      howDidYouHearAboutUs: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    {
      firstName: {
        empty: false,
      },
      lastName: {
        empty: false,
      },
      emailAddress: {
        empty: false,
        invalid: false,
      },
      phoneNumber: {
        empty: false,
        invalid: false,
      },
      consent: {
        empty: false,
      },
    },
    'default',
    source,
  );

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      let { data, newResult } = handleResult();

      await handleSubmitCloudMigration(data, newResult, handleVisibleSection);
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.form_container}>
      <div className={styles.form_header}>
        <h3>{title || 'Get Your Detailed Migration Plan'}</h3>
        <p>
          {instructions ||
            'Fill out the form below to receive a comprehensive cloud migration strategy tailored to your assessment results.'}
        </p>
      </div>

      <form onSubmit={onSubmit} className={styles.form}>
        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="firstName" className={styles.label}>
              {fieldNameFor_FirstName}*
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={values.firstName}
              onChange={e => handleChange(e?.target)}
              className={`${styles.input} ${errors.firstName?.empty ? styles.error : ''}`}
              placeholder="Enter your first name"
            />
            {errors.firstName?.empty && (
              <span className={styles.error_text}>First name is required</span>
            )}
          </div>

          <div className={styles.form_group}>
            <label htmlFor="lastName" className={styles.label}>
              {fieldNameFor_LastName}*
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={values.lastName}
              onChange={e => handleChange(e?.target)}
              className={`${styles.input} ${errors.lastName?.empty ? styles.error : ''}`}
              placeholder="Enter your last name"
            />
            {errors.lastName?.empty && (
              <span className={styles.error_text}>Last name is required</span>
            )}
          </div>
        </div>

        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="phoneNumber" className={styles.label}>
              {fieldNameFor_PhoneNumber}*
            </label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              value={values.phoneNumber}
              onChange={e => handleChange(e?.target)}
              className={`${styles.input} ${errors.phoneNumber?.empty || errors.phoneNumber?.invalid ? styles.error : ''}`}
              placeholder={`${userCountryCode} Enter your phone number`}
            />
            {errors.phoneNumber?.empty && (
              <span className={styles.error_text}>
                Phone number is required
              </span>
            )}
            {errors.phoneNumber?.invalid && (
              <span className={styles.error_text}>
                Please enter a valid phone number
              </span>
            )}
          </div>

          <div className={styles.form_group}>
            <label htmlFor="emailAddress" className={styles.label}>
              {fieldNameFor_EmailAddress}*
            </label>
            <input
              type="email"
              id="emailAddress"
              name="emailAddress"
              value={values.emailAddress}
              onChange={e => handleChange(e?.target)}
              className={`${styles.input} ${errors.emailAddress?.empty || errors.emailAddress?.invalid ? styles.error : ''}`}
              placeholder="Enter your email address"
            />
            {errors.emailAddress?.empty && (
              <span className={styles.error_text}>
                Email address is required
              </span>
            )}
            {errors.emailAddress?.invalid && (
              <span className={styles.error_text}>
                Please enter a valid email address
              </span>
            )}
          </div>
        </div>

        <div className={styles.form_group}>
          <label htmlFor="companyName" className={styles.label}>
            {fieldNameFor_CompanyName}*
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            value={values.companyName}
            onChange={e => handleChange(e?.target)}
            className={styles.input}
            placeholder="Enter your company name"
          />
        </div>

        <div className={styles.consent_group}>
          <label htmlFor="consent" className={styles.consent_label}>
            <input
              type="checkbox"
              id="consent"
              name="consent"
              checked={values.consent}
              onChange={e => handleChange(e?.target)}
            />
            <span>{consent_statement}</span>
          </label>
          {errors.consent?.empty && (
            <span className={styles.error_text}>
              Please accept the consent statement
            </span>
          )}
        </div>

        <div className={styles.submit_section}>
          {isSubmitting ? (
            <div className={styles.spinner_container}>
              <div className={styles.spinner}></div>
            </div>
          ) : (
            <Button
              type="submit"
              className={styles.submit_button}
              label="Get My Migration Plan"
            />
          )}

          {LinkedInButton_title && (
            <a className={styles.linkedin_button} href="#">
              {LinkedInButton_title}
              <Image
                src="https://cdn.marutitech.com/linkedin_c13ca9a536.png"
                width={32}
                height={32}
                alt="LinkedIn Logo"
              />
            </a>
          )}
        </div>
      </form>
    </div>
  );
}
