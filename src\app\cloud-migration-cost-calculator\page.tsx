import { notFound } from 'next/navigation';
import CloudMigrationCalculatorBody from '@components/CloudMigrationCalculatorBody';
import seoSchema from '@utils/seoSchema';

import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getCloudMigrationCalculatorData() {
  return await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=hero_section.image,hero_section.mobile_image,cloud_migration_components.questions.answers,form.formFields,form.button,restart_button,consultation_button,seo.schema',
  );
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata() {
  const cloudMigrationData = await getCloudMigrationCalculatorData();

  if (!cloudMigrationData?.data || cloudMigrationData?.data.length === 0) {
    return {
      title: 'Cloud Migration Cost Calculator',
      description:
        'Calculate your cloud migration costs with our comprehensive assessment tool.',
    };
  }

  return seoSchema(cloudMigrationData?.data?.attributes?.seo);
}

export default async function CloudMigrationCostCalculator() {
  const cloudMigrationData = await getCloudMigrationCalculatorData();
  const formData = await getFormData();

  console.log('cloudMigrationData', cloudMigrationData);

  if (!cloudMigrationData?.data || cloudMigrationData?.data.length === 0) {
    console.log('Cloud Migration Cost Calculator data not found');
    notFound();
  }

  return (
    <>
      {cloudMigrationData?.data?.attributes?.seo && (
        <RichResults data={cloudMigrationData?.data?.attributes?.seo} />
      )}
      {cloudMigrationData?.data?.attributes && (
        <CloudMigrationCalculatorBody
          body={cloudMigrationData?.data?.attributes}
          formData={formData?.data?.attributes?.form}
        />
      )}
    </>
  );
}
