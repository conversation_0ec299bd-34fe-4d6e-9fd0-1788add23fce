import { NextRequest, NextResponse } from 'next/server';
import sendDataToHubspot from 'common/sendDataToHubSpot';
import sendDataToSendGrid from 'common/sendDataToSendGrid';
import sendToSlack from 'common/sendDataToSlack';

export async function POST(request: NextRequest) {
  try {
    const form_data = await request.json();

    // Prepare form fields for HubSpot
    const formFields = [
      {
        name: 'firstname',
        value: form_data?.firstName ?? '',
      },
      {
        name: 'lastname',
        value: form_data?.lastName ?? '',
      },
      {
        name: 'email',
        value: form_data?.emailAddress ?? '',
      },
      {
        name: 'phone',
        value: form_data?.phoneNumber ?? '',
      },
      {
        name: 'company',
        value: form_data?.companyName ?? '',
      },
      {
        name: 'how_can_we_help_you',
        value: form_data?.howCanWeHelpYou ?? '',
      },
      {
        name: 'how_did_you_hear_about_us',
        value: form_data?.howDidYouHearAboutUs ?? '',
      },
      {
        name: 'source',
        value: form_data?.source ?? 'CloudMigrationCalculator',
      },
      {
        name: 'secondary_source',
        value: form_data?.secondary_source ?? '',
      },
      {
        name: 'utm_source',
        value: form_data?.utm_source ?? '',
      },
      {
        name: 'utm_medium',
        value: form_data?.utm_medium ?? '',
      },
      {
        name: 'utm_campaign',
        value: form_data?.utm_campaign ?? '',
      },
      {
        name: 'utm_term',
        value: form_data?.utm_term ?? '',
      },
      {
        name: 'utm_content',
        value: form_data?.utm_content ?? '',
      },
      {
        name: 'gclid',
        value: form_data?.gclid ?? '',
      },
      {
        name: 'fbclid',
        value: form_data?.fbclid ?? '',
      },
      {
        name: 'ip_address',
        value: form_data?.ip_address ?? '',
      },
      {
        name: 'country',
        value: form_data?.country ?? '',
      },
      {
        name: 'region',
        value: form_data?.region ?? '',
      },
      {
        name: 'city',
        value: form_data?.city ?? '',
      },
      // Cloud Migration specific fields
      {
        name: 'migration_cost_estimate',
        value: form_data?.migrationCostEstimate ?? '',
      },
      {
        name: 'migration_cost_upper_range',
        value: form_data?.migrationCostUpperRange ?? '',
      },
      {
        name: 'business_infrastructure_assessment',
        value: form_data?.business_infrastructure_assessment ?? '',
      },
      {
        name: 'workload_resource_analysis',
        value: form_data?.workload_resource_analysis ?? '',
      },
      {
        name: 'cloud_provider_deployment',
        value: form_data?.cloud_provider_deployment ?? '',
      },
      {
        name: 'security_compliance_strategy',
        value: form_data?.security_compliance_strategy ?? '',
      },
      {
        name: 'post_migration_optimization',
        value: form_data?.post_migration_optimization ?? '',
      },
      {
        name: 'total_migration_score',
        value: form_data?.total_migration_score ?? '',
      },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      // Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID,
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_FORM_TEMPLATE_ID,
          form_data,
        );

        // Send Data to success slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        );

        return NextResponse.json(
          {
            message: 'Form submitted successfully',
            status: 'success',
          },
          { status: 200 },
        );
      } else {
        // Send Data to error slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL,
        );

        return NextResponse.json(
          {
            message: 'Failed to submit form to HubSpot',
            status: 'error',
          },
          { status: 500 },
        );
      }
    } catch (error) {
      console.error('Error submitting form:', error);

      // Send Data to error slack channel
      await sendToSlack(
        { ...form_data, error: error.message },
        process.env.NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL,
      );

      return NextResponse.json(
        {
          message: 'Internal server error',
          status: 'error',
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Error parsing request:', error);
    return NextResponse.json(
      {
        message: 'Invalid request format',
        status: 'error',
      },
      { status: 400 },
    );
  }
}
