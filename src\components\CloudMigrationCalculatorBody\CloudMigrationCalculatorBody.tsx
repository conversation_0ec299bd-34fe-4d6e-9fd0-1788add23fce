'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './CloudMigrationCalculatorBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import QuestionAndAnswers from '@components/QuestionAndAnswers';
import Heading from '@components/Heading';
import Button from '@components/Button';
import HeroSection from '@components/HeroSection';
// import CloudMigrationStep from '@components/CloudMigrationStep';
// import CloudMigrationForm from '@components/CloudMigrationForm';
import breakpoints from '@styles/breakpoints.module.css';

interface MigrationResult {
  totalCost: number;
  upperRange: number;
  costBreakdown: Record<string, number>;
}

interface CloudMigrationCalculatorBodyProps {
  body: any;
  formData: any;
}

export default function CloudMigrationCalculatorBody({
  body,
  formData,
}: CloudMigrationCalculatorBodyProps) {
  const router = useRouter();
  const [data, setData] = useState([]);
  const [error, setError] = useState([]);
  const [visibleSection, setVisibleSection] = useState(0);
  const [result, setResult] = useState<MigrationResult>({
    totalCost: 0,
    upperRange: 0,
    costBreakdown: {},
  });

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  useEffect(() => {
    initializeData();
  }, [body]);

  function initializeData() {
    let newData = [];
    let newError = [];
    let newVisibleSection = 0;
    let newResult: MigrationResult = {
      totalCost: 0,
      upperRange: 0,
      costBreakdown: {},
    };

    if (
      localStorage.getItem('cloudMigrationData') !== null &&
      localStorage.getItem('cloudMigrationError') !== null
    ) {
      newData = JSON.parse(localStorage.getItem('cloudMigrationData'));
      newError = JSON.parse(localStorage.getItem('cloudMigrationError'));
    } else {
      for (let i = 0; i < body?.cloud_migration_components?.data.length; i++) {
        let arrData = [];
        let arrError = [];

        for (
          let j = 0;
          j <
          body?.cloud_migration_components?.data[i]?.attributes?.questions
            .length;
          j++
        ) {
          const question =
            body?.cloud_migration_components?.data[i]?.attributes?.questions[j];
          if (question.type === 'mcq' || question.type === 'checkbox') {
            arrData.push([null, null]);
            arrError.push(null);
          } else if (question.type === 'range') {
            arrData.push([question.answers[0].name, question.answers[0].value]);
            arrError.push(false);
          }
        }
        newData[i] = arrData;
        newError[i] = arrError;
      }
    }

    if (localStorage.getItem('cloudMigrationVisibleSection') !== null) {
      newVisibleSection = JSON.parse(
        localStorage.getItem('cloudMigrationVisibleSection'),
      );
    }

    setData(newData);
    setError(newError);
    setVisibleSection(newVisibleSection);
    setResult(newResult);
  }

  function handleData(sectionIndex, questionIndex, name, value) {
    const newData = [...data];
    newData[sectionIndex][questionIndex][0] = name;
    newData[sectionIndex][questionIndex][1] = value;
    localStorage.setItem('cloudMigrationData', JSON.stringify(newData));
    setData(newData);
  }

  function handleError(sectionIndex, questionIndex, value) {
    const newError = [...error];
    newError[sectionIndex][questionIndex] = value;
    localStorage.setItem('cloudMigrationError', JSON.stringify(newError));
    setError(newError);
  }

  function canGoToNext() {
    if (visibleSection < data.length) {
      for (let i = 0; i < error[visibleSection].length; i++) {
        if (
          error[visibleSection][i] === true ||
          error[visibleSection][i] === null
        ) {
          return false;
        }
      }
    }
    return true;
  }

  function handleNext() {
    if (canGoToNext()) {
      const newVisibleSection = visibleSection + 1;
      setVisibleSection(newVisibleSection);
      localStorage.setItem(
        'cloudMigrationVisibleSection',
        JSON.stringify(newVisibleSection),
      );
    }
  }

  function handlePrevious() {
    if (visibleSection > 0) {
      const newVisibleSection = visibleSection - 1;
      setVisibleSection(newVisibleSection);
      localStorage.setItem(
        'cloudMigrationVisibleSection',
        JSON.stringify(newVisibleSection),
      );
    }
  }

  function handleRestart() {
    localStorage.removeItem('cloudMigrationData');
    localStorage.removeItem('cloudMigrationError');
    localStorage.removeItem('cloudMigrationVisibleSection');
    localStorage.removeItem('cloudMigrationResult');
    setVisibleSection(0);
    initializeData();
  }

  function handleVisibleSection(value) {
    setVisibleSection(value);
  }

  // Cloud Migration Cost Calculation Logic based on spreadsheet
  function calculateMigrationCost() {
    if (!canGoToNext()) return { data, newResult: {} };

    let totalCost = 0;
    let costBreakdown = {};
    let serverCost = 0;
    let storageCost = 0;

    // First pass: Calculate base infrastructure costs (servers and storage)
    for (let i = 0; i < data.length; i++) {
      const sectionData = data[i];
      const sectionName =
        body?.cloud_migration_components?.data[i]?.attributes?.heading;

      for (let j = 0; j < sectionData.length; j++) {
        const answer = sectionData[j];
        const questionData =
          body?.cloud_migration_components?.data[i]?.attributes?.questions[j];

        if (answer[1] !== null && answer[1] !== undefined) {
          const questionName = questionData.name.toLowerCase();

          // Store server and storage costs for high availability calculation
          if (questionName.includes('servers')) {
            serverCost = calculateQuestionCost(questionData, answer[1]);
          } else if (
            questionName.includes('capacity') ||
            questionName.includes('storage')
          ) {
            storageCost = calculateQuestionCost(questionData, answer[1]);
          }
        }
      }
    }

    // Second pass: Calculate all costs including high availability
    for (let i = 0; i < data.length; i++) {
      const sectionData = data[i];
      const sectionName =
        body?.cloud_migration_components?.data[i]?.attributes?.heading;

      for (let j = 0; j < sectionData.length; j++) {
        const answer = sectionData[j];
        const questionData =
          body?.cloud_migration_components?.data[i]?.attributes?.questions[j];

        if (answer[1] !== null && answer[1] !== undefined) {
          // Apply cost calculation based on question type and answer
          const cost = calculateQuestionCost(
            questionData,
            answer[1],
            serverCost,
            storageCost,
          );
          totalCost += cost;

          if (cost > 0) {
            costBreakdown[`${sectionName}_${questionData.name}`] = cost;
          }
        }
      }
    }

    const newResult = {
      totalCost: Math.round(totalCost),
      upperRange: Math.round(totalCost * 1.3), // 30% higher for upper range
      costBreakdown: costBreakdown,
    };

    localStorage.setItem('cloudMigrationResult', JSON.stringify(newResult));
    setResult(newResult);
    return { data, newResult };
  }

  function calculateQuestionCost(
    question,
    answerValue,
    serverCost = 0,
    storageCost = 0,
  ) {
    // Cost calculation logic based on the spreadsheet
    const questionName = question.name.toLowerCase();

    if (questionName.includes('servers')) {
      // Server count cost calculation
      if (answerValue <= 10) return 10000;
      if (answerValue <= 50) return 50000;
      if (answerValue <= 100) return 250000;
      return 500000;
    }

    if (questionName.includes('capacity') || questionName.includes('storage')) {
      // Storage capacity cost calculation
      if (answerValue <= 50) return 100;
      if (answerValue <= 200) return 500;
      if (answerValue <= 1000) return 2000;
      if (answerValue <= 10000) return 10000;
      if (answerValue <= 50000) return 50000;
      return 150000;
    }

    if (
      questionName.includes('availability') ||
      questionName.includes('disaster')
    ) {
      // High availability cost (20% of infrastructure + data costs)
      // If user selects "Yes" (answerValue = 1), add 20% of combined server and storage costs
      // If user selects "No" (answerValue = 0), add 0%
      if (answerValue === 1) {
        const combinedInfrastructureCost = serverCost + storageCost;
        return Math.round(combinedInfrastructureCost * 0.2);
      }
      return 0;
    }

    if (questionName.includes('environment')) {
      // Environment costs
      if (questionName.includes('dev')) return 10000;
      if (questionName.includes('test')) return 15000;
      if (questionName.includes('staging')) return 20000;
      if (questionName.includes('production')) return 100000;
    }

    if (questionName.includes('compliance')) {
      // Compliance costs based on type
      const complianceMap = {
        hipaa: 20000,
        gdpr: 10000,
        'pci dss': 15000,
        'soc 2': 15000,
        ccpa: 5000,
        fedramp: 50000,
      };
      return complianceMap[question.name.toLowerCase()] || 0;
    }

    if (questionName.includes('strategy')) {
      // Migration strategy costs
      const strategyMap = {
        'lift-and-shift': 5000,
        're-platforming': 50000,
        're-architecting': 150000,
        hybrid: 100000,
      };
      return strategyMap[question.name.toLowerCase()] || 0;
    }

    if (questionName.includes('auto-scaling')) {
      return answerValue === 1 ? 10000 : 0; // Yes = 1, No = 0
    }

    return 0;
  }

  function handleResult() {
    return calculateMigrationCost();
  }

  return (
    <>
      {body?.hero_section && (
        <HeroSection
          heroData={body?.hero_section}
          className={styles.hero_section}
        />
      )}

      {visibleSection < data.length && (
        <div className={styles.container}>
          <div className={styles.step_container}>
            <div>
              Step {visibleSection + 1} of{' '}
              {body?.cloud_migration_components?.data.length}
            </div>
          </div>

          {body?.cloud_migration_components?.data.map(
            (section, sectionIndex) => (
              <div
                key={sectionIndex}
                className={
                  visibleSection === sectionIndex
                    ? styles.section_wrapper
                    : styles.hidden
                }
              >
                <div className={styles.heading}>
                  <h2>
                    {sectionIndex + 1}. {section?.attributes?.heading}
                  </h2>
                </div>
                {visibleSection !== data.length && (
                  <QuestionAndAnswers
                    sectionIndex={sectionIndex}
                    sectionQuestions={section?.attributes.questions}
                    sectionData={data[sectionIndex]}
                    sectionError={error[sectionIndex]}
                    handleData={handleData}
                    handleError={handleError}
                  />
                )}
                <span id="error">
                  {visibleSection < data.length &&
                    error[visibleSection].includes(true) && (
                      <div className={styles.error_message}>
                        Please fill all the required fields.
                      </div>
                    )}
                </span>

                <div className={styles.button_wrapper}>
                  {visibleSection > 0 && (
                    <Button
                      className={styles.previous_button}
                      label="Previous"
                      type="button"
                      onClick={handlePrevious}
                    />
                  )}
                  {visibleSection < data.length - 1 ? (
                    <Button
                      className={styles.next_button}
                      label="Next"
                      type="button"
                      onClick={handleNext}
                    />
                  ) : (
                    <Button
                      className={styles.calculate_button}
                      label="Calculate Migration Cost"
                      type="button"
                      onClick={() => {
                        const { newResult } = calculateMigrationCost();
                        if (Object.keys(newResult).length > 0) {
                          setVisibleSection(data.length);
                        }
                      }}
                    />
                  )}
                </div>
              </div>
            ),
          )}
        </div>
      )}

      {visibleSection === data.length && Object.keys(result).length > 0 && (
        <div className={styles.result_section}>
          <div className={styles.cost_display}>
            <h2>Your Estimated Cloud Migration Cost</h2>
            <div className={styles.cost_range}>
              <span className={styles.cost_amount}>
                ${result.totalCost?.toLocaleString()} – $
                {result.upperRange?.toLocaleString()} USD
              </span>
            </div>
            <p className={styles.disclaimer}>
              Disclaimer: This estimate is a ballpark figure derived from
              typical migration patterns and industry benchmarks. Actual costs
              can vary depending on your unique setup, complexity needs,
              application dependencies, and more nuanced business and technical
              requirements.
            </p>
          </div>

          <div className={styles.benefits_section}>
            <h3>Turn Your Estimate Into Real Savings</h3>
            <p>
              Our experienced cloud consultants can help you optimize your
              strategy, avoid hidden costs, and potentially reduce your
              migration expenses through informed planning, architecture
              optimization, and vendor selection.
            </p>

            <div className={styles.benefits_grid}>
              <div className={styles.benefit_card}>
                <h4>Slash Your Cloud Spend</h4>
                <p>by up to 30%</p>
                <span>
                  Implement cost-effective migration strategies that align
                  resources to demand and eliminate wasteful spend.
                </span>
              </div>

              <div className={styles.benefit_card}>
                <h4>Accelerate Time-to-Value</h4>
                <span>
                  with cloud-advanced migration roadmaps that minimize downtime
                  and keep your business running smoothly throughout every
                  stage.
                </span>
              </div>

              <div className={styles.benefit_card}>
                <h4>Maximize ROI with Expert Guidance</h4>
                <span>
                  Avoid costly migration missteps by leveraging our deep
                  migration expertise and proven methodologies.
                </span>
              </div>
            </div>
          </div>

          <div className={styles.form_section}>
            <div>
              <h3>Contact Form</h3>
              <p>Form will be implemented here</p>
            </div>
          </div>

          <div className={styles.restart_section}>
            <Button
              className={styles.restart_button}
              label={body?.restart_button?.title || 'Restart Assessment'}
              type="button"
              onClick={handleRestart}
            />
          </div>
        </div>
      )}
    </>
  );
}
