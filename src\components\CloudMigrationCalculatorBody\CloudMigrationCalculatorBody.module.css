@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.container {
  padding: 36px 0 80px 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 36px 0 36px 0;
  }
}

.step_container {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.hidden {
  display: none;
}

.section_wrapper {
  max-width: 1192px;
  margin: 0 auto;
  padding: 40px 0;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }
}

.heading {
  margin-bottom: 32px;
}

.heading h2 {
  font-size: 32px;
  font-weight: 600;
  line-height: 120%;
  color: colorBlack;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 24px;
  }
}

.button_wrapper {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    align-items: center;
  }
}

.previous_button,
.next_button,
.calculate_button {
  padding: 13px 35.5px;
  border-radius: 3px;
  border-width: 2px;
  font-weight: 600;
  font-size: 16px;
  line-height: 148%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.next_button,
.calculate_button {
  color: colorWhite;
  background: linear-gradient(90deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
}

.previous_button {
  color: colorBlack;
  background-color: colorWhite;
  border: 2px solid gray300;
}

.error_message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}

.result_section {
  max-width: 1192px;
  display: flex;
  flex-direction: column;
  gap: 60px;
  margin: 0 auto;
  padding: 60px 0;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
    gap: 40px;
    padding: 40px 0;
  }
}

.cost_display {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.cost_display h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: colorBlack;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 28px;
  }
}

.cost_range {
  margin: 30px 0;
}

.cost_amount {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(90deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 32px;
  }
}

.disclaimer {
  font-size: 14px;
  color: #6c757d;
  margin-top: 20px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.benefits_section {
  text-align: center;
}

.benefits_section h3 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 16px;
  color: colorBlack;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 24px;
  }
}

.benefits_section > p {
  font-size: 18px;
  color: #6c757d;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 16px;
  }
}

.benefits_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.benefit_card {
  background: colorWhite;
  padding: 30px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefit_card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.benefit_card h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: colorBlack;
}

.benefit_card p {
  font-size: 24px;
  font-weight: 700;
  color: brandColorThree;
  margin-bottom: 12px;
}

.benefit_card span {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
}

.form_section {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 12px;
  border: 1px solid #dee2e6;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 30px 20px;
  }
}

.restart_section {
  text-align: center;
}

.restart_button {
  display: flex !important;
  gap: 10px !important;
  margin: 0 auto !important;

  padding: 13px 35.5px !important;
  border-radius: 3px !important;
  border-width: 2px !important;

  font-weight: 600 !important;
  font-size: 16px !important;

  color: colorBlack !important;
  background-image: linear-gradient(colorWhite, colorWhite),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%) !important;
}

.hero_section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
