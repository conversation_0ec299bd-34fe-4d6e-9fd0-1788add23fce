@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024 from breakpoints;

.form_container {
  max-width: 800px;
  margin: 0 auto;
}

.form_header {
  text-align: center;
  margin-bottom: 40px;
}

.form_header h3 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
  color: colorBlack;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 24px;
  }
}

.form_header p {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.6;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form_row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.form_group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  font-weight: 600;
  color: colorBlack;
}

.input {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  background-color: colorWhite;
}

.input:focus {
  outline: none;
  border-color: brandColorThree;
}

.input.error {
  border-color: #e74c3c;
}

.error_text {
  font-size: 12px;
  color: #e74c3c;
  margin-top: 4px;
}

.consent_group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.consent_label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
  cursor: pointer;
}

.consent_label input[type="checkbox"] {
  margin-top: 2px;
  flex-shrink: 0;
}

.submit_section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.submit_button {
  padding: 16px 40px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  color: colorWhite !important;
  background: linear-gradient(90deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%) !important;
  border: none !important;
  cursor: pointer !important;
  transition: transform 0.3s ease !important;
}

.submit_button:hover {
  transform: translateY(-2px);
}

.spinner_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid brandColorThree;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.linkedin_button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background-color: #0077b5;
  color: colorWhite;
  text-decoration: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.linkedin_button:hover {
  background-color: #005885;
}
