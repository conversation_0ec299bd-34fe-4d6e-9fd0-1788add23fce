'use client';

import React from 'react';
import styles from './CloudMigrationStep.module.css';

interface CloudMigrationStepProps {
  visibleSection: number;
  totalSections: number;
}

export default function CloudMigrationStep({ visibleSection, totalSections }: CloudMigrationStepProps) {
  const progressPercentage = ((visibleSection + 1) / totalSections) * 100;

  return (
    <div className={styles.step_container}>
      <div className={styles.step_info}>
        <span className={styles.step_text}>
          Step {visibleSection + 1} of {totalSections}
        </span>
        <span className={styles.progress_text}>
          {Math.round(progressPercentage)}% Complete
        </span>
      </div>
      <div className={styles.progress_bar}>
        <div 
          className={styles.progress_fill}
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
    </div>
  );
}
